.sidebar {
  background-color: var(--secondary-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: relative;
  min-height: 100vh;
}

.sidebar--open {
  width: 280px;
}

.sidebar--closed {
  width: 60px;
}

.sidebar__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

/* Header */
.sidebar__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.sidebar__logo {
  cursor: pointer;
}

.sidebar__logo h2 {
  color: var(--accent-color);
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar--closed .sidebar__logo h2 {
  display: none;
}

.sidebar__toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar__toggle:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

/* New Chat Button */
.sidebar__new-chat {
  padding: 16px;
}

.sidebar__new-chat-btn {
  width: 100%;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.sidebar--closed .sidebar__new-chat-btn {
  padding: 12px;
  justify-content: center;
}

.sidebar__new-chat-btn:hover {
  background-color: var(--accent-hover);
  transform: translateY(-1px);
}

.sidebar__new-chat-icon {
  font-size: 18px;
  font-weight: bold;
}

/* Navigation */
.sidebar__nav {
  flex: 1;
  padding: 8px 16px;
}

.sidebar__nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar__nav-item {
  width: 100%;
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
  text-align: left;
}

.sidebar--closed .sidebar__nav-item {
  justify-content: center;
  padding: 12px;
}

.sidebar__nav-item:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.sidebar__nav-item--active {
  background-color: var(--card-bg);
  color: var(--accent-color);
}

.sidebar__nav-icon {
  font-size: 18px;
  min-width: 20px;
  text-align: center;
}

.sidebar__nav-label {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar--closed .sidebar__nav-label {
  display: none;
}

/* Bottom Section */
.sidebar__bottom {
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

/* User Profile */
.sidebar__user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  background-color: var(--card-bg);
  margin-top: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.sidebar__user-profile:hover {
  background-color: var(--accent-hover);
}

.sidebar--closed .sidebar__user-profile {
  justify-content: center;
  padding: 12px;
}

.sidebar__user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.sidebar__user-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.sidebar__user-avatar-placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--accent-color);
  color: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.sidebar__user-info {
  flex: 1;
  min-width: 0;
}

.sidebar--closed .sidebar__user-info {
  display: none;
}

.sidebar__user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar__user-plan {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Logout Button */
.sidebar__logout-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
}

.sidebar__logout-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* New Sections */
.sidebar__section {
  margin-bottom: 24px;
}

.sidebar__section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 8px 16px;
}

.sidebar__section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar__section-action {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 16px;
  line-height: 1;
  transition: all 0.2s ease;
}

.sidebar__section-action:hover {
  color: var(--accent-color);
  background: var(--hover-bg);
}

.sidebar__items {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.sidebar__item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 8px;
  margin: 0 8px;
  transition: all 0.2s ease;
}

.sidebar__item:hover {
  background: var(--hover-bg);
}

.sidebar__item--active {
  background: var(--accent-color);
  color: var(--primary-bg);
}

.sidebar__item--active:hover {
  color: #ffffff;
}

.sidebar__item-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.sidebar__item-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.sidebar__item-delete {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  transition: all 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar__item:hover .sidebar__item-delete {
  opacity: 1;
}

.sidebar__item-delete:hover {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.sidebar__item-delete svg {
  width: 14px;
  height: 14px;
}

.sidebar__item--active .sidebar__item-delete {
  color: var(--primary-bg);
}

.sidebar__item--active .sidebar__item-delete:hover {
  background-color: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.sidebar__empty {
  padding: 16px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
  font-style: italic;
}

/* Project Container */
.sidebar__project-container {
  margin-bottom: 4px;
}

.sidebar__project-item {
  position: relative;
  font-weight: 600;
}

.sidebar__item-expand-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.sidebar__expand-arrow {
  font-size: 12px;
  color: var(--text-secondary);
  margin-left: auto;
  transition: transform 0.2s ease;
}

.sidebar__project-item:hover .sidebar__expand-arrow {
  color: var(--text-primary);
}

/* Project Threads */
.sidebar__project-threads {
  margin-left: 16px;
  border-left: 2px solid var(--border-color);
  padding-left: 8px;
  margin-top: 4px;
}

.sidebar__thread-item {
  font-weight: 400;
  font-size: 13px;
  padding: 6px 12px;
  margin-left: 0;
}

.sidebar__thread-item .sidebar__item-icon {
  font-size: 14px;
}

.sidebar__project-empty {
  margin-left: 16px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
}

/* Project New Chat Button */
.sidebar__project-new-chat {
  margin-bottom: 4px;
}

.sidebar__project-new-chat-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 6px 12px;
  margin: 0;
  background: none;
  border: 1px dashed var(--border-color);
  border-radius: 6px;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 13px;
  font-weight: 400;
  width: 100%;
  transition: all 0.2s ease;
}

.sidebar__project-new-chat-btn:hover {
  background: var(--hover-bg);
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.sidebar__project-new-chat-btn .sidebar__item-icon {
  font-size: 14px;
  font-weight: bold;
}

.sidebar__project-new-chat-btn .sidebar__item-text {
  font-size: 13px;
  font-weight: 500;
}

.sidebar__loading {
  padding: 16px;
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

/* Search Section */
.sidebar__search {
  padding: 0 8px 16px 8px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.sidebar__search-form {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.sidebar__search-input {
  flex: 1;
  background: var(--primary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 8px 12px;
  color: var(--text-primary);
  font-size: 14px;
}

.sidebar__search-input:focus {
  outline: none;
  border-color: var(--accent-color);
}

.sidebar__search-btn {
  background: var(--accent-color);
  border: none;
  border-radius: 6px;
  padding: 8px 12px;
  color: var(--primary-bg);
  cursor: pointer;
  font-size: 14px;
}

.sidebar__search-results {
  max-height: 200px;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar--open {
    width: 100%;
    position: absolute;
    z-index: 1000;
    height: 100vh;
  }

  .sidebar--closed {
    display: none;
  }
}
